/**
 * Feature Check Hook
 *
 * This hook provides methods for checking if a user has access to a feature
 * based on their subscription plan.
 */

import { writable, derived, get } from 'svelte/store';
import { FeatureAccessLevel } from '$lib/models/features/features';
import type { Feature } from '$lib/models/features/types';

// Types for feature check results
export interface FeatureCheckResult {
  hasAccess: boolean;
  accessLevel?: FeatureAccessLevel;
  feature?: Feature;
  planFeature?: any;
  plan?: any;
  limits?: any[];
  limitReached?: boolean;
  limitId?: string;
  error?: string;
  loading: boolean;
}

// Cache for feature check results
const featureCheckCache = new Map<string, FeatureCheckResult>();

/**
 * Create a feature check hook
 * @returns Methods for checking feature access
 */
export function useFeatureCheck() {
  // Store for feature check results
  const featureChecks = writable<Record<string, FeatureCheckResult>>({});

  /**
   * Check if a user has access to a feature
   * @param featureId The feature ID
   * @param limitId Optional limit ID to check
   * @returns A store with the feature check result
   */
  function checkFeature(featureId: string, limitId?: string) {
    // Create a unique key for this feature check
    const key = limitId ? `${featureId}:${limitId}` : featureId;

    // Check if we already have a result for this feature
    const existingCheck = get(featureChecks)[key];
    if (existingCheck && !existingCheck.loading) {
      // If we have a cached result, return it
      return derived(featureChecks, ($featureChecks) => $featureChecks[key]);
    }

    // Check if we have a cached result
    const cachedResult = featureCheckCache.get(key);
    if (cachedResult) {
      // Update the store with the cached result
      featureChecks.update((checks) => ({
        ...checks,
        [key]: cachedResult,
      }));

      // Return the derived store
      return derived(featureChecks, ($featureChecks) => $featureChecks[key]);
    }

    // Set the initial loading state
    featureChecks.update((checks) => ({
      ...checks,
      [key]: {
        hasAccess: false,
        loading: true,
      },
    }));

    // Fetch the feature check result
    fetchFeatureCheck(featureId, limitId)
      .then((result) => {
        // Update the store with the result
        featureChecks.update((checks) => ({
          ...checks,
          [key]: {
            ...result,
            loading: false,
          },
        }));

        // Cache the result
        featureCheckCache.set(key, {
          ...result,
          loading: false,
        });
      })
      .catch((error) => {
        console.error('Error checking feature access:', error);

        // Update the store with the error
        featureChecks.update((checks) => ({
          ...checks,
          [key]: {
            hasAccess: false,
            error: error.message || 'Failed to check feature access',
            loading: false,
          },
        }));
      });

    // Return the derived store
    return derived(featureChecks, ($featureChecks) => $featureChecks[key]);
  }

  /**
   * Check if a user has access to multiple features
   * @param featureIds The feature IDs to check
   * @param limits Optional map of feature IDs to limit IDs
   * @returns A store with the feature check results
   */
  function checkFeatures(featureIds: string[], limits?: Record<string, string>) {
    // Create a unique key for each feature check
    const keys = featureIds.map((featureId) => {
      const limitId = limits?.[featureId];
      return limitId ? `${featureId}:${limitId}` : featureId;
    });

    // Check if we already have results for all features
    const existingChecks = get(featureChecks);
    const allExist = keys.every((key) => existingChecks[key] && !existingChecks[key].loading);

    if (allExist) {
      // If we have cached results for all features, return them
      return derived(featureChecks, ($featureChecks) => {
        const results: Record<string, FeatureCheckResult> = {};
        for (const key of keys) {
          results[key] = $featureChecks[key];
        }
        return results;
      });
    }

    // Set the initial loading state for each feature
    featureChecks.update((checks) => {
      const newChecks = { ...checks };
      for (const key of keys) {
        if (!newChecks[key]) {
          newChecks[key] = {
            hasAccess: false,
            loading: true,
          };
        }
      }
      return newChecks;
    });

    // Fetch the feature check results
    fetchFeatureChecks(featureIds, limits)
      .then((results) => {
        // Update the store with the results
        featureChecks.update((checks) => {
          const newChecks = { ...checks };
          for (const featureId in results) {
            const limitId = limits?.[featureId];
            const key = limitId ? `${featureId}:${limitId}` : featureId;
            newChecks[key] = {
              ...results[featureId],
              loading: false,
            };

            // Cache the result
            featureCheckCache.set(key, {
              ...results[featureId],
              loading: false,
            });
          }
          return newChecks;
        });
      })
      .catch((error) => {
        console.error('Error checking feature access:', error);

        // Update the store with the error
        featureChecks.update((checks) => {
          const newChecks = { ...checks };
          for (const key of keys) {
            newChecks[key] = {
              hasAccess: false,
              error: error.message || 'Failed to check feature access',
              loading: false,
            };
          }
          return newChecks;
        });
      });

    // Return the derived store
    return derived(featureChecks, ($featureChecks) => {
      const results: Record<string, FeatureCheckResult> = {};
      for (const key of keys) {
        results[key] = $featureChecks[key];
      }
      return results;
    });
  }

  /**
   * Clear the feature check cache
   */
  function clearCache() {
    featureCheckCache.clear();
    featureChecks.set({});
  }

  return {
    checkFeature,
    checkFeatures,
    clearCache,
  };
}

/**
 * Fetch a feature check result from the API
 * @param featureId The feature ID
 * @param limitId Optional limit ID to check
 * @returns The feature check result
 */
async function fetchFeatureCheck(featureId: string, limitId?: string): Promise<FeatureCheckResult> {
  try {
    // Build the URL
    let url = `/api/feature-check?featureId=${encodeURIComponent(featureId)}`;
    if (limitId) {
      url += `&limitId=${encodeURIComponent(limitId)}`;
    }

    // Fetch the result
    const response = await fetch(url);
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to check feature access');
    }

    // Parse the result
    const result = await response.json();
    return {
      ...result,
      loading: false,
    };
  } catch (error) {
    console.error('Error fetching feature check:', error);
    throw error;
  }
}

/**
 * Fetch multiple feature check results from the API
 * @param featureIds The feature IDs to check
 * @param limits Optional map of feature IDs to limit IDs
 * @returns The feature check results
 */
async function fetchFeatureChecks(
  featureIds: string[],
  limits?: Record<string, string>
): Promise<Record<string, FeatureCheckResult>> {
  try {
    // Fetch the results
    const response = await fetch('/api/feature-check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        features: featureIds,
        limits,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to check feature access');
    }

    // Parse the results
    const { results } = await response.json();
    return results;
  } catch (error) {
    console.error('Error fetching feature checks:', error);
    throw error;
  }
}
