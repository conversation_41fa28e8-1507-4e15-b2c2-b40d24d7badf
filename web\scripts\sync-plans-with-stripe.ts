/**
 * Sync Plans with <PERSON><PERSON> Script
 *
 * This script syncs plans from the database with <PERSON><PERSON>.
 * It can be run with: npx ts-node scripts/sync-plans-with-stripe.ts
 */

import { PrismaClient } from '@prisma/client';
import { syncPlanWithStripe } from '../src/lib/server/plan-sync';
import type { PlanTier } from '../src/lib/models/features/types';

const prisma = new PrismaClient();

/**
 * Convert a database plan to a PlanTier object
 */
function dbPlanToPlanTier(dbPlan: any): PlanTier {
  return {
    id: dbPlan.id,
    name: dbPlan.name,
    description: dbPlan.description,
    section: dbPlan.section,
    monthlyPrice: dbPlan.monthlyPrice,
    annualPrice: dbPlan.annualPrice,
    stripePriceMonthlyId: dbPlan.stripePriceMonthlyId,
    stripePriceYearlyId: dbPlan.stripePriceYearlyId,
    popular: dbPlan.popular,
    features: dbPlan.PlanFeature.map((feature: any) => ({
      featureId: feature.featureId,
      accessLevel: feature.accessLevel,
      limits: feature.PlanFeatureLimit?.map((limit: any) => ({
        limitId: limit.limitId,
        value: limit.value === 'unlimited' ? 'unlimited' : parseInt(limit.value, 10),
      })),
    })),
  };
}

/**
 * Sync all paid plans with Stripe
 */
async function syncPlansWithStripe() {
  try {
    console.log('Syncing plans with Stripe...');

    // Get all paid plans from the database
    const plans = await prisma.plan.findMany({
      where: {
        OR: [{ monthlyPrice: { gt: 0 } }, { annualPrice: { gt: 0 } }],
      },
      include: {
        PlanFeature: {
          include: {
            PlanFeatureLimit: true,
          },
        },
      },
    });

    console.log(`Found ${plans.length} paid plans to sync with Stripe`);

    // Convert database plans to PlanTier objects
    const planTiers = plans.map(dbPlanToPlanTier);

    // Sync each plan with Stripe
    const results = await Promise.all(
      planTiers.map(async (plan) => {
        try {
          console.log(`Syncing plan ${plan.id} (${plan.name}) with Stripe...`);
          const updatedPlan = await syncPlanWithStripe(plan);

          // Update the plan in the database with the new Stripe price IDs
          await prisma.plan.update({
            where: { id: plan.id },
            data: {
              stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
              stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
              updatedAt: new Date(),
            },
          });

          return {
            id: plan.id,
            name: plan.name,
            success: true,
            stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,
            stripePriceYearlyId: updatedPlan.stripePriceYearlyId,
          };
        } catch (error) {
          console.error(`Error syncing plan ${plan.id} (${plan.name}) with Stripe:`, error);
          return {
            id: plan.id,
            name: plan.name,
            success: false,
            error: error.message,
          };
        }
      })
    );

    // Count successful and failed syncs
    const successful = results.filter((result) => result.success).length;
    const failed = results.filter((result) => !result.success).length;

    console.log(`Successfully synced ${successful} plans with Stripe`);
    if (failed > 0) {
      console.log(`Failed to sync ${failed} plans with Stripe`);
    }

    return results;
  } catch (error) {
    console.error('Error syncing plans with Stripe:', error);
    throw error;
  }
}

/**
 * Main function to run the sync script
 */
async function main() {
  try {
    console.log('Starting Stripe sync script...');

    // Sync plans with Stripe
    const results = await syncPlansWithStripe();

    // Log results
    console.log('Sync results:');
    results.forEach((result) => {
      if (result.success) {
        console.log(`✅ ${result.name} (${result.id}): Successfully synced`);
        console.log(`  - Monthly price ID: ${result.stripePriceMonthlyId}`);
        console.log(`  - Yearly price ID: ${result.stripePriceYearlyId}`);
      } else {
        console.log(`❌ ${result.name} (${result.id}): Failed to sync`);
        console.log(`  - Error: ${result.error}`);
      }
    });

    console.log('Stripe sync script completed!');
  } catch (error) {
    console.error('Error running Stripe sync script:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
